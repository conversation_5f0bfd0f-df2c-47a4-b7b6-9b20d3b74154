# n8n Integration Documentation

This project now includes integration with n8n workflows through webhooks, allowing you to process chat messages using n8n automation workflows instead of or alongside OpenRouter.

## Overview

The chatbot can now send messages to an n8n webhook URL for processing. This enables you to:
- Create custom automation workflows in n8n
- Process messages through multiple services
- Add custom business logic
- Integrate with databases, APIs, and other services
- Create complex conversation flows

## Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# n8n Integration
# Set to "true" to use n8n webhook instead of OpenRouter for chat
USE_N8N=true
```

### Webhook URL

The n8n webhook URL is currently configured in the code:
```
https://kamdevo.app.n8n.cloud/webhook-test/ba109214-875e-4a54-8c3f-a61d3afcf9c0
```

## API Endpoints

### POST /api/n8n

Direct endpoint to send messages to n8n webhook.

**Request Body:**
```json
{
  "message": "Hello from the chatbot",
  "user_id": "optional_user_id",
  "session_id": "optional_session_id",
  "metadata": {
    "custom": "data"
  }
}
```

**Response:**
```json
{
  "success": true,
  "response": "Response from n8n",
  "data": { /* Raw n8n response */ }
}
```

### POST /api/chat

The main chat endpoint now supports both OpenRouter and n8n based on the `USE_N8N` environment variable.

When `USE_N8N=true`:
- Sends the last user message to n8n webhook
- Includes message history as metadata
- Returns n8n response as chat response

When `USE_N8N=false`:
- Uses OpenRouter API (original behavior)
- Requires `OPENROUTER_API_KEY` environment variable

## n8n Webhook Payload

When sending messages to n8n, the following payload structure is used:

```typescript
{
  message: string;           // The user's message
  user_id: string;          // Unique user identifier
  session_id: string;       // Session identifier
  timestamp: string;        // ISO timestamp
  metadata: {
    model?: string;         // AI model being used
    messageHistory?: Array; // Previous messages in conversation
    [key: string]: any;     // Additional custom data
  }
}
```

## Setting up n8n Workflow

1. **Create a new workflow in n8n**
2. **Add a Webhook trigger node**
   - Set the webhook path to match your URL
   - Configure HTTP method as POST
   - Set response mode to "Respond to Webhook"

3. **Process the incoming data**
   - Access the message via `{{ $json.message }}`
   - Access user_id via `{{ $json.user_id }}`
   - Access metadata via `{{ $json.metadata }}`

4. **Add your business logic**
   - Connect to databases
   - Call external APIs
   - Process with AI services
   - Apply custom rules

5. **Return a response**
   - Use a "Respond to Webhook" node
   - Return JSON with `response` field:
   ```json
   {
     "response": "Your bot's response message"
   }
   ```

## Testing

### Test n8n endpoint directly:
```bash
curl -X POST http://localhost:8080/api/n8n \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello from test"}'
```

### Test chat with n8n:
```bash
curl -X POST http://localhost:8080/api/chat \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}'
```

## Error Handling

- **404 Webhook not found**: The n8n webhook is not active. Click "Execute workflow" in n8n interface.
- **Connection errors**: Check n8n service availability and webhook URL.
- **Timeout errors**: n8n workflow may be taking too long to respond.

## Switching Between Services

You can easily switch between n8n and OpenRouter by changing the environment variable:

```bash
# Use n8n
USE_N8N=true

# Use OpenRouter
USE_N8N=false
```

Restart the server after changing environment variables.

## Frontend Integration

The frontend chat components automatically work with both services. No changes needed to the React components - they continue to use the `/api/chat` endpoint which handles the routing internally.

## Security Considerations

- The webhook URL is currently hardcoded. Consider moving it to environment variables for production.
- Implement authentication if needed for the n8n webhook.
- Validate and sanitize all incoming data in your n8n workflow.
- Consider rate limiting for the webhook endpoints.

## Troubleshooting

1. **Webhook not responding**: Ensure the n8n workflow is active and the webhook trigger is properly configured.
2. **Environment variable not working**: Restart the development server after changing `.env` file.
3. **CORS issues**: The server includes CORS middleware, but check n8n CORS settings if needed.
4. **Response format**: Ensure your n8n workflow returns a proper JSON response with a `response` field.

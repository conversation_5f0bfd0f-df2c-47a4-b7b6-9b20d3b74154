---
description: Keep component and files small
globs:
alwaysApply: true
---

As development goes, break down complex UI into smaller components, avoiding very nested JSX trees.
Also, even within a single page (route), feel free to create auxiliary extra smaller files to make the page more maintenable.
Breaking big components into multiple files will help down the road when the user asks for changes or refactors.

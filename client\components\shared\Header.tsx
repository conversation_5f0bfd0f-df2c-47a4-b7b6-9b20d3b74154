import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

export function Header() {
  const { pathname } = useLocation();
  return (
    <header className="sticky top-0 z-10 border-b bg-background/70 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="mx-auto max-w-5xl px-4 py-4 flex items-center justify-between">
        <Link to="/" className="flex items-center gap-3">
          <div className="size-8 rounded-md bg-primary" />
          <div>
            <div className="text-xs uppercase tracking-widest text-muted-foreground">
              Makers Tech
            </div>
            <h1 className="text-lg font-semibold">ChatBot de Inventario</h1>
          </div>
        </Link>
        <nav className="flex items-center gap-4 text-sm">
          <Link
            to="/"
            className={cn(
              "text-muted-foreground hover:text-foreground transition-colors",
              pathname === "/" && "text-foreground",
            )}
          >
            Chat
          </Link>
          <Link
            to="/catalogo"
            className={cn(
              "text-muted-foreground hover:text-foreground transition-colors",
              (pathname === "/catalogo" || pathname === "/catalogos") &&
                "text-foreground",
            )}
          >
            Catálogo
          </Link>
        </nav>
      </div>
    </header>
  );
}

import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import type { N8nWebhookRequest, N8nWebhookResponse } from "@shared/api";

const OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions";
const N8N_WEBHOOK_URL = "https://kamdevo.app.n8n.cloud/webhook-test/ba109214-875e-4a54-8c3f-a61d3afcf9c0";

export const handleChat: RequestHandler = async (req, res) => {
  try {
    // Check if we should use n8n instead of OpenRouter
    const useN8n = process.env.USE_N8N === "true";

    const { messages, model } = req.body ?? {};

    if (!Array.isArray(messages)) {
      res
        .status(400)
        .json({ error: "Invalid request: 'messages' must be an array" });
      return;
    }

    if (useN8n) {
      // Use n8n webhook for chat
      const lastMessage = messages[messages.length - 1];
      if (!lastMessage || lastMessage.role !== "user") {
        res.status(400).json({ error: "Last message must be from user" });
        return;
      }

      const webhookPayload: N8nWebhookRequest = {
        message: lastMessage.content,
        user_id: `user_${Date.now()}`,
        session_id: `session_${Date.now()}`,
        timestamp: new Date().toISOString(),
        metadata: {
          model: model ?? "deepseek/deepseek-chat-v3.1:free",
          messageHistory: messages.slice(0, -1) // Send previous messages as context
        }
      };

      const response = await fetch(N8N_WEBHOOK_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(webhookPayload),
      });

      if (!response.ok) {
        const text = await response.text().catch(() => "");
        res
          .status(response.status)
          .json({ error: "n8n webhook error", detail: text });
        return;
      }

      let n8nData;
      try {
        n8nData = await response.json();
      } catch (e) {
        n8nData = await response.text();
      }

      // Extract response content from n8n
      const content = typeof n8nData === "string"
        ? n8nData
        : n8nData?.response || n8nData?.message || "Response received from n8n";

      res.json({
        content,
        raw: n8nData,
      });
    } else {
      // Use OpenRouter for chat
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        res.status(500).json({ error: "Server missing OPENROUTER_API_KEY" });
        return;
      }

      const siteUrl = process.env.OPENROUTER_SITE_URL ?? undefined;
      const siteTitle = process.env.OPENROUTER_SITE_TITLE ?? undefined;

      const body = {
        model: model ?? "deepseek/deepseek-chat-v3.1:free",
        messages,
      } as const;

      const response = await fetch(OPENROUTER_URL, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
          ...(siteUrl ? { "HTTP-Referer": siteUrl } : {}),
          ...(siteTitle ? { "X-Title": siteTitle } : {}),
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const text = await response.text().catch(() => "");
        res
          .status(response.status)
          .json({ error: "Upstream error", detail: text });
        return;
      }

      const data = await response.json();

      // Normalize to a minimal response shape for the client
      const content: string | undefined = data?.choices?.[0]?.message?.content;

      res.json({
        content: content ?? "",
        raw: data,
      });
    }
  } catch (err) {
    console.error("/api/chat error", err);
    res.status(500).json({ error: "Unexpected server error" });
  }
};

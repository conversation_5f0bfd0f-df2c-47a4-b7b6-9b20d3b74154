import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

const OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions";

export const handleChat: RequestHandler = async (req, res) => {
  try {
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      res.status(500).json({ error: "Server missing OPENROUTER_API_KEY" });
      return;
    }

    const { messages, model } = req.body ?? {};

    if (!Array.isArray(messages)) {
      res
        .status(400)
        .json({ error: "Invalid request: 'messages' must be an array" });
      return;
    }

    const siteUrl = process.env.OPENROUTER_SITE_URL ?? undefined;
    const siteTitle = process.env.OPENROUTER_SITE_TITLE ?? undefined;

    const body = {
      model: model ?? "deepseek/deepseek-chat-v3.1:free",
      messages,
    } as const;

    const response = await fetch(OPENROUTER_URL, {
      method: "POST",
      headers: {
        Authorization: `Bear<PERSON> ${apiKey}`,
        "Content-Type": "application/json",
        ...(siteUrl ? { "HTTP-Referer": siteUrl } : {}),
        ...(siteTitle ? { "X-Title": siteTitle } : {}),
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const text = await response.text().catch(() => "");
      res
        .status(response.status)
        .json({ error: "Upstream error", detail: text });
      return;
    }

    const data = await response.json();

    // Normalize to a minimal response shape for the client
    const content: string | undefined = data?.choices?.[0]?.message?.content;

    res.json({
      content: content ?? "",
      raw: data,
    });
  } catch (err) {
    console.error("/api/chat error", err);
    res.status(500).json({ error: "Unexpected server error" });
  }
};

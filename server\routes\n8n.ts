import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import type { N8nWebhookRequest, N8nWebhookResponse } from "@shared/api";

const N8N_WEBHOOK_URL = "https://kamdevo.app.n8n.cloud/webhook-test/ba109214-875e-4a54-8c3f-a61d3afcf9c0";

export const handleN8nWebhook: RequestHandler = async (req, res) => {
  try {
    const { message, user_id, session_id, metadata } = req.body ?? {};

    if (!message || typeof message !== "string") {
      res.status(400).json({ 
        success: false, 
        error: "Invalid request: 'message' is required and must be a string" 
      });
      return;
    }

    // Prepare the payload for n8n webhook
    const webhookPayload: N8nWebhookRequest = {
      message: message.trim(),
      user_id: user_id || `user_${Date.now()}`,
      session_id: session_id || `session_${Date.now()}`,
      timestamp: new Date().toISOString(),
      metadata: metadata || {}
    };

    // Send message to n8n webhook
    const response = await fetch(N8N_WEBHOOK_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(webhookPayload),
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => "");
      console.error("n8n webhook error:", response.status, errorText);
      res.status(response.status).json({ 
        success: false, 
        error: "n8n webhook error", 
        data: errorText 
      });
      return;
    }

    // Parse n8n response
    let n8nData;
    try {
      n8nData = await response.json();
    } catch (e) {
      // If response is not JSON, treat as text
      n8nData = await response.text();
    }

    // Return successful response
    const successResponse: N8nWebhookResponse = {
      success: true,
      response: typeof n8nData === "string" ? n8nData : n8nData?.response || "Message sent successfully",
      data: n8nData,
    };

    res.json(successResponse);
  } catch (err) {
    console.error("/api/n8n error", err);
    res.status(500).json({ 
      success: false, 
      error: "Unexpected server error" 
    });
  }
};

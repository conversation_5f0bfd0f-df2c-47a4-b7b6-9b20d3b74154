import { useCallback, useMemo, useState } from "react";
import type {
  ChatMessage,
  ChatRequestBody,
  ChatResponseBody,
} from "@shared/api";

export function useChat(initialSystemMessage?: string) {
  const [messages, setMessages] = useState<ChatMessage[]>(() => [
    initialSystemMessage
      ? { role: "assistant", content: initialSystemMessage }
      : {
          role: "assistant",
          content:
            "<PERSON><PERSON>, soy el asistente de Makers Tech. Puedo ayudarte con consultas sobre inventario, características y precios. Escribe tu pregunta para comenzar.",
        },
  ]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);

  const canSend = useMemo(
    () => input.trim().length > 0 && !loading,
    [input, loading],
  );

  const send = useCallback(
    async (text?: string) => {
      const content = (text ?? input).trim();
      if (!content || loading) return;

      setInput("");
      const nextHistory: ChatMessage[] = [
        ...messages,
        { role: "user", content },
      ];
      setMessages(nextHistory);
      setLoading(true);

      try {
        const body: ChatRequestBody = {
          messages: nextHistory,
          model: "deepseek/deepseek-chat-v3.1:free",
        };

        const res = await fetch("/api/chat", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body),
        });
        if (!res.ok) throw new Error(await res.text());
        const data = (await res.json()) as ChatResponseBody;
        const reply = data.content || "";
        setMessages((prev) => [...prev, { role: "assistant", content: reply }]);
      } catch (e) {
        setMessages((prev) => [
          ...prev,
          {
            role: "assistant",
            content: "Lo siento, ocurrió un error. Inténtalo otra vez.",
          },
        ]);
      } finally {
        setLoading(false);
      }
    },
    [input, loading, messages],
  );

  return { messages, input, setInput, loading, canSend, send } as const;
}

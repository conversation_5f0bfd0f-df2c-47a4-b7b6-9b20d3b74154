import { useEffect, useMemo, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { Header } from "@/components/shared/Header";
import { cn } from "@/lib/utils";
import type {
  ChatMessage,
  ChatRequestBody,
  ChatResponseBody,
} from "@shared/api";

function startVT(cb: () => void) {
  // Progressive enhancement for View Transitions
  // @ts-expect-error experimental API
  const vt = document.startViewTransition as
    | undefined
    | ((fn: () => void) => any);
  if (vt) vt(() => cb());
  else cb();
}

export default function Index() {
  const [messages, setMessages] = useState<ChatMessage[]>(() => [
    {
      role: "assistant",
      content:
        "<PERSON><PERSON>, soy el asistente de Makers Tech. Puedo ayudarte con consultas sobre inventario, características y precios. Escribe tu pregunta para comenzar.",
    },
  ]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const scrollerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollerRef.current?.scrollTo({
      top: scrollerRef.current.scrollHeight,
      behavior: "smooth",
    });
  }, [messages, loading]);

  const canSend = useMemo(
    () => input.trim().length > 0 && !loading,
    [input, loading],
  );

  const sendMessage = async () => {
    const text = input.trim();
    if (!text) return;

    setInput("");
    const nextHistory: ChatMessage[] = [
      ...messages,
      { role: "user", content: text },
    ];
    startVT(() => setMessages(nextHistory));
    setLoading(true);

    try {
      const body: ChatRequestBody = {
        messages: nextHistory,
        model: "deepseek/deepseek-chat-v3.1:free",
      };

      const res = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });

      if (!res.ok) {
        const errText = await res.text();
        throw new Error(errText || "Error del servidor");
      }

      const data = (await res.json()) as ChatResponseBody;
      const content = data.content || "";
      startVT(() =>
        setMessages((prev) => [...prev, { role: "assistant", content }]),
      );
    } catch (err) {
      console.error(err);
      startVT(() =>
        setMessages((prev) => [
          ...prev,
          {
            role: "assistant",
            content:
              "Lo siento, no pude procesar tu solicitud en este momento. Por favor, inténtalo nuevamente.",
          },
        ]),
      );
    } finally {
      setLoading(false);
    }
  };

  const onKeyDown: React.KeyboardEventHandler<HTMLTextAreaElement> = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (canSend) void sendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-white to-slate-50 dark:from-slate-950 dark:via-slate-950 dark:to-black">
      <Header />

      <main className="mx-auto max-w-3xl px-4 pb-28 pt-6">
        <div
          ref={scrollerRef}
          className="h-[calc(100dvh-220px)] w-full overflow-y-auto rounded-2xl border bg-card/80 p-4 shadow-sm"
          style={{ viewTransitionName: "chat" }}
        >
          <ul className="space-y-4">
            {messages.map((m, idx) => (
              <li
                key={idx}
                className={cn(
                  "flex gap-3 animate-in fade-in slide-in-from-bottom-2 duration-300",
                  m.role === "user" ? "justify-end" : "justify-start",
                )}
              >
                {m.role === "assistant" && (
                  <div className="size-8 shrink-0 rounded-full bg-primary/80" />
                )}
                <div
                  className={cn(
                    "max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm",
                    m.role === "user"
                      ? "bg-primary text-primary-foreground rounded-br-sm"
                      : "bg-muted text-foreground rounded-bl-sm",
                  )}
                >
                  {m.content}
                </div>
                {m.role === "user" && (
                  <div className="size-8 shrink-0 rounded-full bg-slate-300 dark:bg-slate-600" />
                )}
              </li>
            ))}

            {loading && (
              <li className="flex gap-3 animate-in fade-in slide-in-from-bottom-2 duration-300">
                <div className="size-8 shrink-0 rounded-full bg-primary/80" />
                <div className="max-w-[85%] rounded-2xl rounded-bl-sm p-0.5">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-60" />
                    <Skeleton className="h-4 w-44" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
              </li>
            )}
          </ul>
        </div>
      </main>

      <Composer
        value={input}
        onChange={setInput}
        onSend={sendMessage}
        canSend={canSend}
        onKeyDown={onKeyDown}
      />
    </div>
  );
}

function Composer({
  value,
  onChange,
  onSend,
  canSend,
  onKeyDown,
}: {
  value: string;
  onChange: (v: string) => void;
  onSend: () => void;
  canSend: boolean;
  onKeyDown: React.KeyboardEventHandler<HTMLTextAreaElement>;
}) {
  return (
    <div className="fixed inset-x-0 bottom-0 border-t bg-background/70 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="mx-auto max-w-3xl px-4 py-4">
        <div className="grid grid-cols-[1fr_auto] gap-3 items-end">
          <Textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={onKeyDown}
            rows={2}
            placeholder="Escribe tu mensaje... (Shift+Enter para nueva línea)"
            className="resize-none"
          />
          <Button onClick={onSend} disabled={!canSend} className="h-10 px-5">
            Enviar
          </Button>
        </div>
        <p className="mt-2 text-center text-xs text-muted-foreground">
          Desarrollado por Juan Camilo Morales Martínez
        </p>
      </div>
    </div>
  );
}

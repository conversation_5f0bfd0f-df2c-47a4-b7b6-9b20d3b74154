import { useEffect, useMemo, useRef, useState } from "react";
import { Head<PERSON> } from "@/components/shared/Header";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { useChat } from "@/hooks/useChat";
import { cn } from "@/lib/utils";
import { MessageCircle, X } from "lucide-react";

function startVT(cb: () => void) {
  // @ts-expect-error experimental API
  const vt = document.startViewTransition as
    | undefined
    | ((fn: () => void) => any);
  if (vt) vt(() => cb());
  else cb();
}

const PRODUCTS = [
  { id: "p1", name: "Laptop Pro 14", price: 5499000, stock: 8, tag: "Nuevo" },
  { id: "p2", name: 'Monitor 27" 4K', price: 1799000, stock: 12, tag: "Top" },
  {
    id: "p3",
    name: "Te<PERSON>lado Mecánico RGB",
    price: 399000,
    stock: 25,
    tag: "Popular",
  },
  {
    id: "p4",
    name: "Headset Inalámbrico",
    price: 499000,
    stock: 5,
    tag: "Oferta",
  },
  {
    id: "p5",
    name: "Mouse Gamer 8K",
    price: 299000,
    stock: 30,
    tag: "Popular",
  },
];

function currency(v: number) {
  return new Intl.NumberFormat("es-CO", {
    style: "currency",
    currency: "COP",
    maximumFractionDigits: 0,
  }).format(v);
}

export default function Catalogo() {
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const t = setTimeout(() => setLoading(false), 600);
    return () => clearTimeout(t);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-white to-slate-50 dark:from-slate-950 dark:via-slate-950 dark:to-black">
      <Header />
      <main className="mx-auto max-w-5xl px-4 py-8">
        <h2 className="text-2xl font-semibold mb-4">Catálogo</h2>
        <p className="text-sm text-muted-foreground mb-6">
          Explora productos y consulta disponibilidad en tiempo real con el
          ChatBot.
        </p>
        <section
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
          style={{ viewTransitionName: "catalog" }}
        >
          {(loading ? Array.from({ length: 6 }) : PRODUCTS).map(
            (item: any, i) => (
              <article
                key={item?.id ?? i}
                className="rounded-xl border bg-card/80 p-4 shadow-sm hover:shadow transition-shadow"
              >
                {loading ? (
                  <div className="space-y-3">
                    <Skeleton className="h-36 w-full rounded-lg" />
                    <Skeleton className="h-5 w-2/3" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="h-36 w-full rounded-lg bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-800 dark:to-slate-700" />
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{item.name}</h3>
                      <span className="text-xs rounded-full bg-muted px-2 py-0.5">
                        {item.tag}
                      </span>
                    </div>
                    <div className="text-primary font-semibold">
                      {currency(item.price)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Stock: {item.stock}
                    </div>
                    <Button className="w-full">Agregar al carrito</Button>
                  </div>
                )}
              </article>
            ),
          )}
        </section>
      </main>

      <FloatingChat />
    </div>
  );
}

function FloatingChat() {
  const [open, setOpen] = useState(false);
  const { messages, input, setInput, loading, canSend, send } = useChat(
    "Hola, soy tu asistente. ¿Sobre qué producto necesitas información?",
  );
  const scrollerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    scrollerRef.current?.scrollTo({
      top: scrollerRef.current.scrollHeight,
      behavior: "smooth",
    });
  }, [messages, loading]);

  return (
    <div
      className="fixed right-4 bottom-4 z-50"
      style={{ viewTransitionName: "chat-bubble" }}
    >
      {open && (
        <div className="mb-3 w-[min(92vw,380px)] h-[520px] rounded-2xl border bg-background shadow-2xl overflow-hidden animate-in fade-in zoom-in-95">
          <div className="flex items-center justify-between border-b px-3 py-2">
            <div className="text-sm font-medium">Asistente</div>
            <button
              aria-label="Cerrar"
              onClick={() => setOpen(false)}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="size-4" />
            </button>
          </div>
          <div className="flex h-[calc(520px-44px-64px)] flex-col">
            <div
              ref={scrollerRef}
              className="flex-1 overflow-y-auto p-3 space-y-3"
            >
              {messages.map((m, idx) => (
                <div
                  key={idx}
                  className={cn(
                    "flex gap-2",
                    m.role === "user" ? "justify-end" : "justify-start",
                  )}
                >
                  {m.role === "assistant" && (
                    <div className="size-6 rounded-full bg-primary/80" />
                  )}
                  <div
                    className={cn(
                      "max-w-[80%] rounded-xl px-3 py-2 text-xs shadow-sm",
                      m.role === "user"
                        ? "bg-primary text-primary-foreground rounded-br-sm"
                        : "bg-muted rounded-bl-sm",
                    )}
                  >
                    {m.content}
                  </div>
                </div>
              ))}
              {loading && (
                <div className="flex gap-2">
                  <div className="size-6 rounded-full bg-primary/80" />
                  <div className="space-y-2">
                    <Skeleton className="h-3 w-40" />
                    <Skeleton className="h-3 w-28" />
                  </div>
                </div>
              )}
            </div>
            <div className="border-t p-2 grid grid-cols-[1fr_auto] gap-2">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    if (canSend) send();
                  }
                }}
                rows={2}
                placeholder="Escribe tu mensaje..."
                className="resize-none"
              />
              <Button
                onClick={() => send()}
                disabled={!canSend}
                className="h-9 px-4"
              >
                Enviar
              </Button>
            </div>
            <p className="px-3 pb-2 text-center text-[10px] text-muted-foreground">
              Desarrollado por Juan Camilo Morales Martínez
            </p>
          </div>
        </div>
      )}
      <Button
        aria-label="Abrir chat"
        onClick={() => startVT(() => setOpen((v) => !v))}
        variant={open ? "secondary" : "default"}
        size="lg"
        className="rounded-full shadow-lg h-12 w-12 p-0"
      >
        {open ? <X className="size-5" /> : <MessageCircle className="size-5" />}
      </Button>
    </div>
  );
}

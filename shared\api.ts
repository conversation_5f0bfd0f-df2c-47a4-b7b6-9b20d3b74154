/**
 * Shared code between client and server
 * Useful to share types between client and server
 * and/or small pure JS functions that can be used on both client and server
 */

/**
 * Example response type for /api/demo
 */
export interface DemoResponse {
  message: string;
}

export type ChatRole = "system" | "user" | "assistant" | "tool";

export interface ChatMessage {
  role: ChatRole;
  content: string;
}

export interface ChatRequestBody {
  messages: ChatMessage[];
  model?: string;
}

export interface ChatResponseBody {
  content: string;
  raw: unknown;
}

// n8n Integration Types
export interface N8nWebhookRequest {
  message: string;
  user_id?: string;
  session_id?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

export interface N8nWebhookResponse {
  success: boolean;
  response?: string;
  error?: string;
  data?: unknown;
}
